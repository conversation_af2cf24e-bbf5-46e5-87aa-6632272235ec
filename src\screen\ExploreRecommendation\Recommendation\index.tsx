import { SortIcon } from '@/components/icons';
import RecommendationCard from './card';

const Recommendation = () => {
  const recommendations = [
    {
      title: 'Town Browsing',
      duration: '4 nights / 5 days',
      location: 'Aspen',
      tags: 'Activities, Explore, Leisure, Family',
      image:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/f56e3da898f545cc17a53e95ffdbb8997aece058.png',
      imagesm1:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/83ed2c2e9d06705ccf95533f2aee7336a6f0cd21.png',
      imagesm2:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/6d672094a1fd448a55dec604f1e5fd9c8bfc45a7.png',
      imagesm3:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/714c2ddf6bde0d6dadedfdebf6952a5065ab44b3.jpg',
      imagesm4:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/0dba40eef6717cc00067b6fba7fa88075a53729c.png',
      badge: 'badge',
    },
    {
      title: 'Mountain Retreat',
      duration: '3 nights / 4 days',
      location: 'Colorado',
      tags: 'Nature, Relax, Hiking',
      image: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/d660485870cb81c7a62b6c5333498708c73dc85e.png',
      imagesm1:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/83ed2c2e9d06705ccf95533f2aee7336a6f0cd21.png',
      imagesm2:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/6d672094a1fd448a55dec604f1e5fd9c8bfc45a7.png',
      imagesm3:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/714c2ddf6bde0d6dadedfdebf6952a5065ab44b3.jpg',
      imagesm4:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/0dba40eef6717cc00067b6fba7fa88075a53729c.png',
      badge: 'Top Rated',
    },
    {
      title: 'City Lights',
      duration: '2 nights / 3 days',
      location: 'New York',
      tags: 'Urban, Nightlife, Shopping',
      image: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/9e1ea916a44b53e9b68c0b1724e24658f97f4f3b.png',
      imagesm1:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/83ed2c2e9d06705ccf95533f2aee7336a6f0cd21.png',
      imagesm2:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/6d672094a1fd448a55dec604f1e5fd9c8bfc45a7.png',
      imagesm3:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/714c2ddf6bde0d6dadedfdebf6952a5065ab44b3.jpg',
      imagesm4:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/0dba40eef6717cc00067b6fba7fa88075a53729c.png',
      badge: 'Hot',
    },
    {
      title: 'Beach Paradise',
      duration: '5 nights / 6 days',
      location: 'Hawaii',
      tags: 'Beach, Relaxation, Water Sports',
      image: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/f56e3da898f545cc17a53e95ffdbb8997aece058.png',
      imagesm1:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/83ed2c2e9d06705ccf95533f2aee7336a6f0cd21.png',
      imagesm2:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/6d672094a1fd448a55dec604f1e5fd9c8bfc45a7.png',
      imagesm3:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/714c2ddf6bde0d6dadedfdebf6952a5065ab44b3.jpg',
      imagesm4:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/0dba40eef6717cc00067b6fba7fa88075a53729c.png',
      badge: 'Popular',
    },
    {
      title: 'Cultural Journey',
      duration: '6 nights / 7 days',
      location: 'Japan',
      tags: 'Culture, History, Food',
      image: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/9e1ea916a44b53e9b68c0b1724e24658f97f4f3b.png',
      imagesm1:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/83ed2c2e9d06705ccf95533f2aee7336a6f0cd21.png',
      imagesm2:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/6d672094a1fd448a55dec604f1e5fd9c8bfc45a7.png',
      imagesm3:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/714c2ddf6bde0d6dadedfdebf6952a5065ab44b3.jpg',
      imagesm4:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/0dba40eef6717cc00067b6fba7fa88075a53729c.png',
      badge: 'New',
    },
  ];
  return (
    <div className=" relative">
      <div className="flex flex-row items-center justify-between flex-shrink-0 md:sticky md:top-0 md:z-50 mb-2 bg-[#F2F2FF]">
        <div>
          <p className="text-lg font-bold">Recommendation</p>
        </div>
        <div className="flex flex-row items-center gap-2">
          <SortIcon isAnimation={false} className="text-default-Secondary" />
          <p className="text-sm font-medium text-default-Secondary">Filter</p>
        </div>
      </div>

      {recommendations.map(item => (
        <RecommendationCard recommendations={item} />
      ))}
    </div>
  );
};

export default Recommendation;
