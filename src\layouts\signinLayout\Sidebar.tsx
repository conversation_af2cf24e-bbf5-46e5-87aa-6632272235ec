'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { FiChevronDown, FiSettings, FiLogOut } from 'react-icons/fi';
import { Divider } from '@heroui/react';

import * as AnimatedIcons from '@/components/icons';
import './Sidebar.css';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { MENU_SECTIONS, GUEST_MENU_SECTIONS } from './menuData';

type SidebarProps = {
  isOpenFromHeader?: boolean;
  isLogin?: boolean;
  setIsOpenFromHeader?: any;
};

export default function Sidebar({
  isOpenFromHeader = false,
  isLogin,
  setIsOpenFromHeader = () => {},
}: SidebarProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  const handleSettingsClick = () => {
    setIsDropdownOpen(false);
    // For now, we'll create a settings route. You can modify this as needed
    router.push('/settings');
  };

  const handleLogoutClick = () => {
    setIsDropdownOpen(false);
    // Clear user data from localStorage and redirect to home
    localStorage.removeItem('userEmailOrPhone');
    localStorage.removeItem('userPassword');
    // Refresh the page to update the login state
    window.location.reload();
  };

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (isMobile) {
      setIsExpanded(isOpenFromHeader);
    }
  }, [isOpenFromHeader, isMobile]);

  const toggleMobile = () => {
    if (isMobile) {
      setIsExpanded(prev => {
        const newVal = !prev;
        setIsOpenFromHeader(newVal);
        return newVal;
      });
    }
  };

  const menu = isLogin ? MENU_SECTIONS : GUEST_MENU_SECTIONS;

  return (
    <div
      className={`sidebar-wrapper relative w-full ${
        isExpanded ? 'expanded' : ''
      } ${isMobile ? 'mobile' : ''} ${
        !isExpanded && isMobile ? 'collapsed' : ''
      }`}
      onMouseEnter={() => !isMobile && setIsExpanded(true)}
      onMouseLeave={() => !isMobile && !isDropdownOpen && setIsExpanded(false)}
    >
      {isMobile && isExpanded && (
        <button
          type="button"
          className="absolute top-1 right-3 z-50 text-xl text-gray-700"
          onClick={toggleMobile}
        >
          ✕
        </button>
      )}

      {/* Logo */}
      <div className="flex justify-between items-center p-4">
        <div className="flex items-center gap-2">
          <Image
            src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/logo.svg"
            alt="User"
            width={40}
            height={40}
            className="min-w-[40px] max-w-[40px] min-h-[40px] max-h-[40px] "
          />
          <div className={` ${isExpanded ? 'visible' : 'hidden'}`}>
            <span className="logo-text">nxVoy</span>
          </div>
        </div>
      </div>

      {isLogin && isExpanded && (
        <div className="p-3">
          <p className="font-bold text-3xl"> Welcome Back, Danie!</p>
          <p className="text-default-700 text-base pr-4">
            Meet Shasa, Your Travel Companion!
          </p>
        </div>
      )}

      {/* Menu */}
      <div className="flex flex-col flex-1 gap-3 p-3">
        {menu.map((menuSection, sectionIndex) => (
          <div
            key={`section-${sectionIndex}-${menuSection.items[0]?.path || sectionIndex}`}
          >
            {(menuSection as any).section && (
              <div
                className={`sidebar-section border-1 ${isExpanded ? 'visible' : 'invisible'}`}
              >
                {(menuSection as any).section}
              </div>
            )}
            {isExpanded && <Divider className="my-4 text-subtitle" />}
            {menuSection.items.map(({ icon, label, path, isAnimated }) => {
              const IconComponent =
                AnimatedIcons[icon as keyof typeof AnimatedIcons];
              const isCurrentRoute = pathname === path;

              return (
                <Link
                  href={path}
                  key={label}
                  className={`sidebar-link flex items-center hover:bg-[#F5F5F5] ${
                    isExpanded ? 'justify-start' : 'justify-center'
                  } ${isCurrentRoute ? 'recommend border-2 border-primary-200' : ''}`}
                >
                  <div
                    className={`icon-wrapper ${isCurrentRoute ? 'text-primary-200' : ''}`}
                  >
                    {IconComponent && (
                      <IconComponent
                        size={20}
                        isAnimation={isAnimated}
                        color={isCurrentRoute ? '#707FF5' : 'currentColor'}
                      />
                    )}
                  </div>
                  <span
                    className={`label-text text-subtitle ${isExpanded ? 'visible' : 'hidden'}`}
                  >
                    {label}
                  </span>
                </Link>
              );
            })}
          </div>
        ))}
      </div>

      {/* Profile */}
      {isLogin && (
        <div className="p-4">
          {isExpanded ? (
            <DropdownMenu onOpenChange={setIsDropdownOpen}>
              <DropdownMenuTrigger asChild>
                <button
                  type="button"
                  className="w-full flex justify-between items-center cursor-pointer hover:bg-[#F5F5F5] rounded-lg p-2 transition-colors"
                >
                  <div className="flex items-center gap-2">
                    <Image
                      src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/logo.svg"
                      alt="User"
                      width={40}
                      height={40}
                      className="flex-shrink-0"
                    />
                    <div className="sidebar-profile-info text-left">
                      <span className="sidebar-name block">Danie Jay</span>
                      <span className="sidebar-username block">
                        @Danie.Jay82
                      </span>
                    </div>
                  </div>
                  <FiChevronDown size={16} className="flex-shrink-0" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-56"
                side={isMobile ? 'top' : 'right'}
                align="end"
                sideOffset={8}
              >
                <div className="flex items-center gap-2 px-2 py-1.5">
                  <Image
                    src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/logo.svg"
                    alt="User"
                    width={32}
                    height={32}
                  />
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">Danie Jay</span>
                    <span className="text-xs text-gray-500">@Danie.Jay82</span>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleSettingsClick}
                  className="cursor-pointer"
                >
                  <FiSettings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleLogoutClick}
                  className="cursor-pointer text-red-600 focus:text-red-600"
                >
                  <FiLogOut className="mr-2 h-4 w-4" />
                  <span>Logout</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex justify-center items-center">
              <Image
                src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/logo.svg"
                alt="User"
                width={40}
                height={40}
                className="flex-shrink-0"
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
}
