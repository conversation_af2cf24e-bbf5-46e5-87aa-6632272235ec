'use client';

import { useState, useEffect } from 'react';

import { HeaderSkeleton, SidebarSkeleton } from '@/components/loaders';

import Header from './Header';
import Sidebar from './Sidebar';

interface LayoutProps {
  children: React.ReactNode;
  isLoading?: boolean;
  showSkeletonFor?: number; // Duration in milliseconds to show skeleton
}

export default function Layout({
  children,
  isLoading = false,
  showSkeletonFor = 0,
}: LayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [showSkeleton, setShowSkeleton] = useState(isLoading);

  // handle login
  const [isLogin, setIsLogin] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      const loginEmail = localStorage.getItem('userEmailOrPhone');
      const loginPassword = localStorage.getItem('userPassword');
      setIsLogin(
        loginEmail === '<EMAIL>' &&
          loginPassword === 'Maddy123!?'
      );
    }); // simulate skeleton for 2 seconds

    return () => clearTimeout(timer);
  }, []);

  // Handle skeleton display duration
  useEffect(() => {
    if (showSkeletonFor > 0) {
      setShowSkeleton(true);
      const timer = setTimeout(() => {
        setShowSkeleton(false);
      }, showSkeletonFor);

      return () => clearTimeout(timer);
    }

    setShowSkeleton(isLoading);
    return undefined;
  }, [isLoading, showSkeletonFor]);

  // Show skeleton layout
  if (showSkeleton) {
    return (
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar Skeleton */}
        <div className="w-64 bg-white border-r border-gray-200">
          <SidebarSkeleton />
        </div>

        {/* Main Content Area */}
        <div className="flex flex-col flex-1">
          {/* Header Skeleton */}
          <div className="sticky top-0 z-10 bg-white border-b border-gray-200">
            <HeaderSkeleton />
          </div>

          {/* Main Content Skeleton */}
          <main className="flex-1 max-h-[calc(100vh-73px)] min-h-[calc(100vh-73px)] overflow-auto ml-6 pt-0 bg-[#F5F5F5] rounded-tl-2xl">
            <div className="p-5 h-full">
              {/* Content skeleton placeholder */}
              <div className="space-y-4">
                <div className="w-48 h-8 bg-gray-200 rounded animate-pulse" />
                <div className="w-96 h-6 bg-gray-200 rounded animate-pulse" />
                <div className="w-full h-64 bg-gray-200 rounded-xl animate-pulse" />
                <div className="grid grid-cols-3 gap-4">
                  <div className="h-32 bg-gray-200 rounded-xl animate-pulse" />
                  <div className="h-32 bg-gray-200 rounded-xl animate-pulse" />
                  <div className="h-32 bg-gray-200 rounded-xl animate-pulse" />
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  // Show actual layout
  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar
        isOpenFromHeader={isSidebarOpen}
        setIsOpenFromHeader={setIsSidebarOpen}
        isLogin={isLogin}
      />
      <div className="flex flex-col flex-1">
        <div className="sticky top-0 z-10 bg-white">
          <Header
            onSidebarToggle={() => setIsSidebarOpen(prev => !prev)}
            isLogin={isLogin}
          />
        </div>
        <main className="flex-1 max-h-[calc(100vh-73px)] min-h-[calc(100vh-73px)] overflow-auto  ml-6  max-md:ml-0 max-md:rounded-tl-none bg-[#F2F2FF] rounded-tl-2xl ">
          <div className=''>
          {children}
          </div>
        </main>
      </div>
    </div>
  );
}
