import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';

import ExploretheWorldCard from './ExploretheWorldCard';

const data = [
  {
    id: 1,
    name: 'Island',
    image: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    rating: 5,
  },
  {
    id: 2,
    name: 'Mountains',
    image: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    rating: 4,
  },
  {
    id: 3,
    name: 'Desert',
    image: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    rating: 3,
  },
  {
    id: 4,
    name: 'Forest',
    image: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    rating: 5,
  },
  {
    id: 5,
    name: 'Cityscape',
    image: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    rating: 4,
  },
];

const ExploretheWorldCarousel = () => {
  return (
    <div className="">
      <Carousel className="w-full mb-0">
        <CarouselContent className="overflow-visible">
          {data.map(item => (
            <CarouselItem
              key={`explore-item-${item.id}`}
              className="md:basis-1/1 lg:basis-1/2 xl:basis-1/3"
            >
              <ExploretheWorldCard item={item} />
            </CarouselItem>
          ))}
        </CarouselContent>

        {/* Custom Previous Button with Icon */}
        <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-[#4D4D4D] hover:bg-[#817f7f] text-white border-0 rounded-full shadow">
          <FiChevronLeft className="w-6 h-6" />
        </CarouselPrevious>

        {/* Custom Next Button with Icon */}
        <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-[#4D4D4D] hover:bg-[#817f7f] text-white border-0 p-2 rounded-full shadow">
          <FiChevronRight className="w-6 h-6" />
        </CarouselNext>
      </Carousel>
    </div>
  );
};

export default ExploretheWorldCarousel;
